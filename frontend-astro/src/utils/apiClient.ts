import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

const getApiBaseUrl = (): string => {
  // Server-side detection
  if (typeof window === 'undefined') {
    return process.env.API_URL || 'http://localhost:8000';
  }
  
  // Client-side detection
  if (import.meta.env.DEV && !import.meta.env.VITE_API_URL?.includes('dev.atlasvip.cloud')) {
    return 'http://localhost:8000';
  }
  
  return import.meta.env.VITE_API_URL || 'https://dev.atlasvip.cloud';
};

export const api: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  withCredentials: true,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  // Add CSRF token if available
  if (typeof document !== 'undefined') {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }
  
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: getApiBaseUrl(),
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

export default api;
